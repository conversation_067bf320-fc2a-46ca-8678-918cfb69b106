"""
Stage 10 Failure Analysis Components for GretahAI ScriptWeaver

Failure analysis, regeneration workflows, and improvement feedback loops components.
Extracted from stage10_components.py for better maintainability.
"""

import streamlit as st
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import core dependencies
from debug_utils import debug

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10_failure_analysis_ui")


def render_failure_analysis_button(test_results, filename):
    """
    Render the failure analysis button when script execution fails.

    Args:
        test_results: Test execution results
        filename: Script filename

    Returns:
        bool: True if analysis button was clicked
    """
    if not test_results or test_results.get('exit_code', 0) == 0:
        return False

    st.markdown("---")
    st.markdown("### 🔍 Failure Analysis")

    # Check if analysis already exists
    analysis_key = f"failure_analysis_{filename}"
    has_existing_analysis = analysis_key in st.session_state

    if has_existing_analysis:
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            st.info("🤖 Failure analysis completed")

        with col2:
            reanalyze_clicked = st.button("🔄 Re-analyze", use_container_width=True, type="secondary")

        with col3:
            clear_analysis_clicked = st.button("🗑️ Clear Analysis", use_container_width=True, type="secondary")

        # Handle clear analysis button
        if clear_analysis_clicked:
            if analysis_key in st.session_state:
                del st.session_state[analysis_key]
            st.success("✅ Failure analysis cleared!")
            st.rerun()
            return False

        # Handle re-analyze button
        if reanalyze_clicked:
            return True

        # Display existing analysis
        analysis_data = st.session_state.get(analysis_key)
        if analysis_data:
            render_failure_analysis_results(analysis_data, st.session_state.get('selected_test_case', {}))

        return False
    else:
        col1, col2 = st.columns([3, 1])

        with col1:
            st.error("❌ **Script execution failed** - AI can analyze the failure and suggest improvements")

        with col2:
            analyze_clicked = st.button("🔍 Analyze Failure", use_container_width=True, type="primary")

        return analyze_clicked


def render_failure_analysis_results(analysis_data, target_test_case):
    """
    Render the AI failure analysis results in a professional format.

    Args:
        analysis_data: AI failure analysis results
        target_test_case: Target test case information
    """
    if not analysis_data:
        st.error("No failure analysis data available.")
        return

    # Extract analysis components
    failure_summary = analysis_data.get('failure_summary', 'No summary available')
    root_causes = analysis_data.get('root_causes', [])
    improvement_recommendations = analysis_data.get('improvement_recommendations', {})
    regeneration_priority = analysis_data.get('regeneration_priority', 'medium')
    confidence_score = analysis_data.get('confidence_score', 0)

    # Analysis header with confidence score
    st.markdown("### 🤖 AI Failure Analysis")

    # Confidence indicator
    if confidence_score >= 80:
        st.success(f"🎯 **High Confidence Analysis** ({confidence_score}%)")
    elif confidence_score >= 60:
        st.warning(f"⚠️ **Medium Confidence Analysis** ({confidence_score}%)")
    else:
        st.error(f"❓ **Low Confidence Analysis** ({confidence_score}%)")

    # Failure summary
    st.markdown("#### 📋 Failure Summary")
    st.info(failure_summary)

    # Root causes analysis
    if root_causes:
        st.markdown("#### 🔍 Root Causes Identified")
        for i, cause in enumerate(root_causes, 1):
            cause_type = cause.get('type', 'Unknown')
            cause_description = cause.get('description', 'No description')
            cause_severity = cause.get('severity', 'medium')
            cause_likelihood = cause.get('likelihood', 'medium')

            # Severity-based styling
            if cause_severity == 'high':
                st.error(f"**{i}. {cause_type}** (Severity: {cause_severity.title()}, Likelihood: {cause_likelihood.title()})")
                st.markdown(f"   {cause_description}")
            elif cause_severity == 'medium':
                st.warning(f"**{i}. {cause_type}** (Severity: {cause_severity.title()}, Likelihood: {cause_likelihood.title()})")
                st.markdown(f"   {cause_description}")
            else:
                st.info(f"**{i}. {cause_type}** (Severity: {cause_severity.title()}, Likelihood: {cause_likelihood.title()})")
                st.markdown(f"   {cause_description}")

    # Improvement recommendations
    if improvement_recommendations:
        st.markdown("#### 💡 Improvement Recommendations")

        # Quick fixes
        quick_fixes = improvement_recommendations.get('quick_fixes', [])
        if quick_fixes:
            st.markdown("**🚀 Quick Fixes:**")
            for fix in quick_fixes:
                st.markdown(f"• {fix}")

        # Code improvements
        code_improvements = improvement_recommendations.get('code_improvements', [])
        if code_improvements:
            st.markdown("**🔧 Code Improvements:**")
            for improvement in code_improvements:
                st.markdown(f"• {improvement}")

        # Structural changes
        structural_changes = improvement_recommendations.get('structural_changes', [])
        if structural_changes:
            st.markdown("**🏗️ Structural Changes:**")
            for change in structural_changes:
                st.markdown(f"• {change}")

        # Regeneration guidance
        regeneration_guidance = improvement_recommendations.get('regeneration_guidance', '')
        if regeneration_guidance:
            st.markdown("**🎯 Regeneration Guidance:**")
            st.info(regeneration_guidance)

    # Priority indicator
    st.markdown("#### ⚡ Regeneration Priority")
    if regeneration_priority == 'high':
        st.error("🚨 **HIGH PRIORITY** - Immediate regeneration strongly recommended")
    elif regeneration_priority == 'medium':
        st.warning("⚠️ **MEDIUM PRIORITY** - Regeneration recommended for better results")
    else:
        st.info("💡 **LOW PRIORITY** - Regeneration optional, minor improvements expected")


def render_regeneration_options(analysis_data, filename):
    """
    Render regeneration options based on failure analysis results.

    Args:
        analysis_data: AI failure analysis results
        filename: Script filename

    Returns:
        tuple: (regenerate_clicked, regeneration_option)
    """
    if not analysis_data:
        return False, None

    st.markdown("---")
    st.markdown("### 🔄 Script Regeneration Options")

    # Get regeneration priority and recommendations
    regeneration_priority = analysis_data.get('regeneration_priority', 'medium')
    recommendations = analysis_data.get('improvement_recommendations', {})
    regeneration_guidance = recommendations.get('regeneration_guidance', '')

    # Priority-based messaging
    if regeneration_priority == 'high':
        st.error("🚨 **High Priority Regeneration Recommended** - Critical issues detected that require immediate attention.")
    elif regeneration_priority == 'medium':
        st.warning("⚠️ **Regeneration Recommended** - Several issues identified that could be resolved with improved generation.")
    else:
        st.info("💡 **Optional Regeneration** - Minor improvements possible through regeneration.")

    # Regeneration guidance
    if regeneration_guidance:
        st.markdown(f"**AI Guidance:** {regeneration_guidance}")

    # Regeneration options
    st.markdown("**Choose regeneration approach:**")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        **🎯 Enhanced Targeted Filling**
        - Use failure analysis to improve gap questions
        - Address newly discovered gaps
        - More precise based on failure insights
        """)

        targeted_key = f"regenerate_targeted_{filename}"
        if st.button("🎯 Regenerate with Enhanced Targeting", key=targeted_key, type="primary", use_container_width=True):
            return True, "enhanced_targeted"

    with col2:
        st.markdown("""
        **🤖 Intelligent Inference with Fixes**
        - AI applies failure analysis insights
        - Automatic fixes for identified issues
        - Faster regeneration with improvements
        """)

        inference_key = f"regenerate_inference_{filename}"
        if st.button("🤖 Regenerate with AI Fixes", key=inference_key, type="secondary", use_container_width=True):
            return True, "enhanced_inference"

    return False, None
