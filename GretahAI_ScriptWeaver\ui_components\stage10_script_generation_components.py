"""
Stage 10 Script Generation Components for GretahAI ScriptWeaver

Script generation controls, AI prompts, and generated script display components.
Extracted from stage10_components.py for better maintainability.
"""

import streamlit as st
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import core dependencies
from debug_utils import debug

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10_script_generation_ui")


def render_script_generation_controls(selected_template, selected_test_case, gap_analysis_data=None, gap_responses=None, gap_handling_option=None):
    """
    Render the script generation controls interface with enhanced gap analysis integration.

    Args:
        selected_template: Selected template script
        selected_test_case: Selected target test case
        gap_analysis_data: Gap analysis results (optional)
        gap_responses: User responses to fill gaps (optional)
        gap_handling_option: Selected gap handling option ('targeted', 'inference', or None)

    Returns:
        tuple: (custom_instructions, preserve_structure, include_error_handling, generate_clicked, enhanced_context)
    """
    with st.expander("🚀 Script Generation", expanded=True):
        # Validate inputs first
        from core.template_helpers import validate_template_generation_inputs

        is_valid, error_message = validate_template_generation_inputs(selected_template, selected_test_case)

        if not is_valid:
            st.error(f"❌ {error_message}")
            return None, None, None, False, None

        st.markdown("**Configure script generation options**")

        # Generation options
        col1, col2 = st.columns(2)

        with col1:
            preserve_structure = st.checkbox(
                "🏗️ Preserve Template Structure",
                value=True,
                help="Maintain the original template's code organization and patterns"
            )

        with col2:
            include_error_handling = st.checkbox(
                "🛡️ Enhanced Error Handling",
                value=True,
                help="Add comprehensive error handling and logging"
            )

        # Custom instructions
        custom_instructions = st.text_area(
            "📝 Custom Instructions (Optional)",
            placeholder="Add specific requirements or modifications...",
            help="Provide additional context or specific requirements for script generation"
        )

        # Enhanced context based on gap analysis
        enhanced_context = {}
        if gap_analysis_data:
            enhanced_context['gap_analysis'] = gap_analysis_data
            enhanced_context['gap_handling_option'] = gap_handling_option
            
            if gap_responses:
                enhanced_context['gap_responses'] = gap_responses
                st.success(f"✅ Gap analysis completed with {len(gap_responses)} responses")
            elif gap_handling_option == "inference":
                st.success("✅ AI inference mode selected for gap handling")
            else:
                st.info("💡 Gap analysis available - consider filling gaps for better results")

        # Generation status and context display
        if enhanced_context:
            with st.expander("📊 Generation Context", expanded=False):
                if gap_analysis_data:
                    compatibility_score = gap_analysis_data.get('compatibility_score', 0)
                    st.metric("Template Compatibility", f"{compatibility_score}%")
                    
                if gap_handling_option:
                    st.info(f"Gap Handling: {gap_handling_option.title()}")
                    
                if gap_responses:
                    st.info(f"User Responses: {len(gap_responses)} gaps filled")

        # Generate button with enhanced messaging
        button_text = "🚀 Generate Script"
        if gap_handling_option == "targeted":
            button_text = "🎯 Generate with Targeted Data"
        elif gap_handling_option == "inference":
            button_text = "🤖 Generate with AI Inference"

        generate_clicked = st.button(button_text, use_container_width=True, type="primary")

        return custom_instructions, preserve_structure, include_error_handling, generate_clicked, enhanced_context


def render_generation_success_display(parsed_script, filename, target_test_case, template_script):
    """
    Render the successful script generation display.

    Args:
        parsed_script: Clean parsed script content
        filename: Generated filename
        target_test_case: Target test case information
        template_script: Template script used
    """
    # Display success message
    st.success("✅ **Script Generated**")

    # Display parsed script
    st.markdown("### 📄 Script")
    st.code(parsed_script, language='python')

    # Download and copy buttons
    col1, col2 = st.columns(2)
    with col1:
        st.download_button(
            label="📥 Download",
            data=parsed_script,
            file_name=filename,
            mime="text/x-python",
            use_container_width=True
        )

    with col2:
        if st.button("📋 Copy", use_container_width=True):
            # Note: Actual clipboard functionality would require additional implementation
            st.success("✅ Script copied to clipboard!")

    # Generation details
    with st.expander("📊 Generation Details", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.info(f"""
            **Generated Script:**
            - Filename: {filename}
            - Lines: {len(parsed_script.split(chr(10)))}
            - Target: {target_test_case.get('Test Case ID', 'Unknown')}
            """)
        
        with col2:
            st.info(f"""
            **Template Used:**
            - ID: {template_script.get('id', 'Unknown')}
            - Type: {template_script.get('type', 'Unknown')}
            - Original: {template_script.get('test_case_id', 'Unknown')}
            """)

    # Store generation timestamp
    generation_timestamp = datetime.now().isoformat()
    debug(f"Script generation completed: {filename} at {generation_timestamp}")
