# GretahAI ScriptWeaver

[![Cogniron Logo](https://cogniron.com/wp-content/uploads/2024/10/image-69.png)](https://cogniron.com/)

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized use, copying, or distribution is strictly prohibited.

---

**GretahAI ScriptWeaver** is an enterprise-grade AI-powered test automation platform that transforms manual test cases into executable PyTest automation scripts. This commercial software leverages advanced AI capabilities, hybrid editing systems, and template-based script generation to deliver production-ready test automation solutions.

## 🚀 Version 2.6.0 - Latest Features

### **Stage 10: Script Playground** 🎮
- **Template-Based Script Generation**: Use optimized scripts as templates for new test cases
- **AI-Powered Gap Analysis**: Intelligent compatibility assessment and missing requirement identification
- **Professional Enterprise UI**: Minimalist, card-based layouts with enterprise styling
- **Independent API Management**: Secure, hidden API key handling with automatic configuration

### **Hybrid AI-Assisted Editing System** 🤖
- **Revolutionary Editing Paradigm**: Seamlessly combine AI-generated steps with manual insertions
- **Visual Step Distinction**: Clear indicators for AI-generated vs manually added steps
- **Real-Time Flow Validation**: Automatic validation with conflict detection
- **Step Template System**: Pre-built templates for common manual operations

### **Enterprise-Grade Features** 🏢
- **Advanced Performance Monitoring**: Real-time CPU, memory, and execution tracking
- **Comprehensive Data Persistence**: Cross-session data reliability with automatic backup
- **Professional Script Browser**: Always-accessible script management with search and filtering
- **Enhanced Security**: Input sanitization, secure session management, and audit logging

## ✨ Core Features

### **AI-Powered Test Automation**
- Parse test cases from Excel files with intelligent analysis
- Convert test cases to automation-ready step tables using Gemini 2.0 Flash
- Automatically detect UI elements from any website with smart filtering
- Interactive element selection for precise locator identification
- Generate context-aware test data for realistic form inputs
- Template-based script generation using optimized scripts as patterns

### **Advanced Workflow Management**
- **10-Stage Workflow**: Complete end-to-end test automation pipeline
- **Hybrid Editing**: Combine AI efficiency with human expertise
- **Script Browser**: Comprehensive script management and history tracking
- **Manual Script Editing**: In-app editor with syntax highlighting and version control
- **Real-Time Validation**: Continuous validation with actionable feedback

### **Enterprise-Grade Reliability**
- **Production-Ready Platform**: Enterprise stability with comprehensive error handling
- **Advanced Monitoring**: Complete system monitoring with performance metrics
- **Persistent Storage**: Reliable data persistence across application restarts
- **Comprehensive Logging**: Detailed AI interaction logs with request correlation
- **Security Features**: Enhanced input validation and secure session management

### **Professional User Experience**
- **Minimalist Design**: Clean, professional interface suitable for enterprise environments
- **Responsive Layouts**: Optimized for different screen sizes with proper accessibility
- **Visual Indicators**: Clear status indicators and progress tracking
- **Enhanced Navigation**: Direct stage access with prerequisite validation
- **Error Recovery**: Intelligent error handling with actionable guidance

## 🏗️ Project Architecture

The project follows an enterprise-grade modular architecture with clean separation of concerns, supporting all 10 workflow stages with advanced UI component extraction:

```
GretahAI_ScriptWeaver/
  ├── app.py                  # Main Streamlit application entry point
  ├── state_manager.py        # Centralized state management with StateStage enum
  ├── stages/                 # Complete 10-stage workflow implementation
  │   ├── __init__.py         # Centralized stage imports and API
  │   ├── stage1.py           # Stage 1: Excel File Upload & Preview
  │   ├── stage2.py           # Stage 2: Website Configuration & API Setup
  │   ├── stage3.py           # Stage 3: Test Case Analysis & AI Conversion
  │   ├── stage4.py           # Stage 4: UI Element Detection & Interactive Selection
  │   ├── stage5.py           # Stage 5: Test Data Configuration & Generation
  │   ├── stage6.py           # Stage 6: Test Script Generation & Review
  │   ├── stage7.py           # Stage 7: Test Script Execution & Results
  │   ├── stage8.py           # Stage 8: Script Optimization & Consolidation
  │   ├── stage9.py           # Stage 9: Script Browser & History Management
  │   └── stage10.py          # Stage 10: Script Playground & Template Generation
  ├── ui_components/          # Enterprise UI component library
  │   ├── __init__.py         # UI components module initialization
  │   ├── hybrid_step_editor.py # Hybrid AI-assisted editing components
  │   ├── script_editor.py    # Manual script editing components
  │   └── stage10_components.py # Stage 10 professional UI components
  ├── core/                   # Advanced core functionality modules
  │   ├── __init__.py         # Core module initialization
  │   ├── ai.py               # Centralized AI/LLM integration with logging
  │   ├── ai_helpers.py       # AI response processing and utilities
  │   ├── template_helpers.py # Template management and analysis
  │   ├── template_prompt_builder.py # Advanced prompt engineering
  │   ├── step_data_storage.py # Persistent JSON-based data storage
  │   ├── performance_monitor.py # Real-time performance tracking
  │   ├── script_storage.py   # SQLite-based script persistence
  │   ├── element_detection.py # Smart UI element discovery
  │   ├── element_matching.py # AI-powered element matching
  │   ├── interactive_selector.py # Interactive element selection
  │   └── navigation_helpers.py # Stage navigation and validation
  ├── utils/                  # Advanced utility modules
  │   ├── flag_helpers.py     # Self-destructing flag system
  │   └── stage_monitor.py    # Comprehensive stage transition monitoring
  ├── docs/                   # Comprehensive documentation
  │   ├── README.md           # Documentation index and navigation
  │   ├── USER_GUIDE.md       # Complete user workflow guide
  │   ├── DEVELOPER_GUIDE.md  # Developer documentation and architecture
  │   ├── API.md              # Complete API reference
  │   ├── DEVELOPMENT.md      # Development guidelines and patterns
  │   └── CONTRIBUTING.md     # Contribution guidelines
  ├── generated_tests/        # Generated and optimized test scripts
  ├── screenshots/            # Test execution screenshots and artifacts
  ├── ai_logs/                # Comprehensive AI interaction logging
  │   ├── requests/           # Successful AI request logs
  │   ├── errors/             # AI error logs with context
  │   └── metrics/            # Performance metrics and analytics
  ├── step_data_storage/      # Persistent step data with URL tracking
  ├── temp_uploads/           # Temporary file storage
  └── script_storage.db       # SQLite database for script persistence
```

## 🏛️ Enterprise Architecture

GretahAI ScriptWeaver implements an enterprise-grade modular architecture designed for scalability, maintainability, and production deployment:

### 1. **Complete 10-Stage Workflow Architecture**
   - **10 Dedicated Stage Modules**: Each application stage (1-10) in its own dedicated file with single responsibility
   - **Stage 10 Script Playground**: Advanced template-based script generation with AI gap analysis
   - **Centralized Import System**: All stages imported through `stages/__init__.py` for API consistency
   - **Independent Stage Development**: Each stage can be developed, tested, and maintained independently
   - **Scalable Design**: Architecture supports easy addition of new stages and workflow extensions

### 2. **Advanced State Management System**
   - **StateManager Pattern**: Centralized StateManager dataclass with comprehensive state tracking
   - **StateStage Enum**: Authoritative stage management preventing invalid transitions
   - **Cross-Session Persistence**: Reliable state persistence across application restarts
   - **Stage Transition Monitoring**: Advanced monitoring with stack traces and state snapshots
   - **Self-Destructing Flags**: Context managers for temporary session flags with automatic cleanup

### 3. **Enterprise UI Component Architecture**
   - **Component Extraction**: UI logic separated into dedicated `ui_components/` modules
   - **Professional Styling**: Enterprise-grade minimalist design with card-based layouts
   - **Reusable Components**: Modular UI components following established design patterns
   - **Responsive Design**: Optimized layouts for different screen sizes with accessibility support
   - **Consistent Visual Hierarchy**: Uniform styling across all application components

### 4. **Advanced AI Integration Framework**
   - **Centralized AI Processing**: All AI interactions routed through `core/ai.py` with comprehensive logging
   - **Gemini 2.0 Flash Integration**: Latest AI model with enhanced performance and capabilities
   - **Request Correlation**: Unique request IDs for all AI interactions with cross-referenced logging
   - **Token Usage Tracking**: Comprehensive token counting and usage statistics with cost estimation
   - **Enhanced Error Handling**: Robust error recovery with detailed diagnostic information

### 5. **Production-Ready Data Management**
   - **Persistent Storage**: JSON-based step data storage with atomic updates and thread safety
   - **SQLite Integration**: Robust script storage with proper indexing and foreign key constraints
   - **URL Tracking**: Comprehensive URL capture during test execution with historical data
   - **Performance Monitoring**: Real-time tracking of CPU, memory, and execution metrics
   - **Backup and Recovery**: Automatic backup creation and data integrity validation

### 6. **Comprehensive Monitoring & Debugging**
   - **Advanced Logging Infrastructure**: Detailed AI interaction logs with request tracking
   - **Performance Monitoring**: Real-time system performance tracking and optimization
   - **Stage Transition Monitoring**: Complete tracking of stage changes with diagnostic information
   - **Debug Data Flow Tools**: Specialized debugging tools for complex workflows
   - **Error Recovery Systems**: Intelligent error handling with actionable guidance

## 🔄 Complete 10-Stage Workflow

GretahAI ScriptWeaver provides a comprehensive end-to-end test automation workflow with advanced features:

### **Core Workflow Stages (1-8)**

1. **Stage 1: Excel File Upload & Preview** 📁
   - Upload Excel files containing test cases with intelligent parsing
   - Preview test cases in interactive data tables with filtering
   - Validate test case format and structure automatically

2. **Stage 2: Website Configuration & API Setup** 🌐
   - Configure target website URL for test automation
   - Set up Google AI API key with secure handling
   - Configure element detection and matching preferences

3. **Stage 3: Test Case Analysis & AI Conversion** 🤖
   - Select test cases from uploaded Excel files
   - Convert test cases to automation-ready step tables using Gemini 2.0 Flash
   - Review converted step tables in markdown and JSON formats
   - Enable hybrid editing for AI + manual step combination

4. **Stage 4: UI Element Detection & Interactive Selection** 🎯
   - Process test steps sequentially with manual progression
   - Automatically detect UI elements with smart filtering
   - Interactive element selection with real-time browser control
   - AI-powered element matching with reasoning explanations

5. **Stage 5: Test Data Configuration & Generation** 📊
   - Generate realistic test data for form inputs using AI
   - Customize test data values with intelligent suggestions
   - Skip test data configuration for navigation-only steps
   - Support for complex data types and validation rules

6. **Stage 6: Test Script Generation & Review** 📝
   - Generate PyTest scripts for individual steps with AI optimization
   - Review generated scripts with syntax highlighting
   - Manual script editing with version tracking
   - Save scripts with proper naming conventions

7. **Stage 7: Test Script Execution & Results** ▶️
   - Execute generated scripts using enhanced PyTest configuration
   - Real-time execution monitoring with performance metrics
   - Capture screenshots, logs, and artifacts on failures
   - Automatic progression to next step or optimization

8. **Stage 8: Script Optimization & Consolidation** 🔧
   - Combine all test step scripts into cohesive test suites
   - AI-powered script optimization for best practices
   - Download final optimized PyTest scripts
   - Return to Stage 3 for new test case selection

### **Advanced Workflow Stages (9-10)**

9. **Stage 9: Script Browser & History Management** 📜
   - Always-accessible script browser independent of workflow stage
   - Comprehensive script history with cross-session persistence
   - Advanced search and filtering capabilities
   - Script comparison with side-by-side diff views
   - Bulk operations and script metadata management

10. **Stage 10: Script Playground & Template Generation** 🎮
    - **Template-Based Script Generation**: Use optimized scripts as templates
    - **AI-Powered Gap Analysis**: Intelligent compatibility assessment
    - **Dynamic Gap Filling**: User-friendly forms for missing requirements
    - **Professional UI**: Enterprise-grade interface with card-based layouts
    - **Script Execution**: Full pytest integration with comprehensive reporting

## 🚀 Installation & Setup

### **Prerequisites**

- **Python 3.8+**: Ensure you have Python 3.8 or higher installed
- **Google AI API Key**: Required for AI-powered features (Gemini 2.0 Flash)
- **Chrome Browser**: Required for interactive element selection
- **Git**: For cloning the repository (if applicable)

### **Installation Steps**

1. **Clone or Extract the Application**
   ```bash
   # If using Git
   git clone <repository-url>
   cd GretahAI_ScriptWeaver

   # Or extract from provided archive
   unzip GretahAI_ScriptWeaver.zip
   cd GretahAI_ScriptWeaver
   ```

2. **Install Dependencies**
   ```bash
   # Install all required Python packages
   pip install -r requirements.txt

   # For development environment
   pip install -r requirements-dev.txt  # If available
   ```

3. **Configure API Keys**

   Create a `config.json` file in the root directory:
   ```json
   {
       "google_api_key": "YOUR_GOOGLE_AI_API_KEY_HERE",
       "model_name": "gemini-2.0-flash",
       "debug_mode": false
   }
   ```

   **Alternative Configuration Methods:**
   - **Environment Variable**: Set `GOOGLE_API_KEY` environment variable
   - **Streamlit Secrets**: Add to `.streamlit/secrets.toml` for deployment

4. **Verify Installation**
   ```bash
   # Test the application startup
   streamlit run app.py --server.headless true --server.port 8501
   ```

### **Configuration Options**

#### **Google AI API Setup**
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key for Gemini models
3. Add the API key to your `config.json` file
4. Ensure billing is enabled for production usage

#### **Optional Environment Variables**
```bash
# Enable debug logging for Stage 10
export SCRIPTWEAVER_DEBUG=true

# Custom model configuration
export GOOGLE_AI_MODEL="gemini-2.0-flash"

# Custom data directory
export SCRIPTWEAVER_DATA_DIR="/path/to/data"
```

#### **Browser Configuration**
- **Chrome/Chromium**: Required for interactive element selection
- **WebDriver**: Automatically managed by the application
- **Headless Mode**: Configurable for server deployments

## 📚 Comprehensive Documentation

For detailed information, please refer to our complete documentation suite:

### **User Documentation**

| Document | Description | Audience |
|----------|-------------|----------|
| [**User Guide**](docs/USER_GUIDE.md) | Complete step-by-step workflow guide with examples | End Users |
| [**Installation Guide**](#-installation--setup) | Detailed setup and configuration instructions | All Users |
| [**Troubleshooting Guide**](#-troubleshooting) | Common issues and solutions | All Users |

### **Developer Documentation**

| Document | Description | Audience |
|----------|-------------|----------|
| [**Developer Guide**](docs/DEVELOPER_GUIDE.md) | Architecture, system design, and development patterns | Developers |
| [**API Documentation**](docs/API.md) | Complete API reference with function signatures | Developers |
| [**Development Guidelines**](docs/DEVELOPMENT.md) | Coding standards, patterns, and best practices | Developers |
| [**Contributing Guide**](docs/CONTRIBUTING.md) | Contribution workflow and guidelines | Contributors |

### **Project Documentation**

| Document | Description | Audience |
|----------|-------------|----------|
| [**Documentation Index**](docs/README.md) | Central navigation for all documentation | All Users |
| [**Changelog**](CHANGELOG.md) | Version history and detailed release notes | All Users |
| [**Architecture Overview**](#-enterprise-architecture) | High-level system architecture | Technical Users |

## 🎯 Usage Guide

### **Running the Application**

Start the Streamlit application:

```bash
cd GretahAI_ScriptWeaver
streamlit run app.py
```

The application will open in your default web browser at `http://localhost:8501`.

**Production Deployment:**
```bash
# For production deployment
streamlit run app.py --server.port 8501 --server.address 0.0.0.0
```

### **Test Case Format Requirements**

The Excel file should contain test cases with the following structure:

#### **Required Columns:**
- **Test Case ID**: Unique identifier (e.g., TC_001, TC_002)
- **Test Case Objective**: Clear description of what the test validates
- **Steps**: Detailed test steps with the following sub-columns:
  - **Step No**: Sequential step number (1, 2, 3, etc.)
  - **Test Steps**: Detailed description of the action to perform
  - **Expected Result**: Expected outcome or validation criteria

#### **Example Test Case Format:**
```
Test Case ID: TC_001
Test Case Objective: Verify user login functionality with valid credentials

Steps:
1. Navigate to login page | Login page should be displayed
2. Enter valid username | Username field should accept input
3. Enter valid password | Password field should accept input
4. Click login button | User should be redirected to dashboard
```

### **Generated Test Scripts Features**

Generated test scripts include enterprise-grade features:

#### **Technical Implementation:**
- **Selenium WebDriver**: Modern browser automation with PyTest framework
- **Explicit Waits**: Reliable element detection with configurable timeouts
- **Smart Assertions**: Context-aware assertions based on expected results
- **Screenshot Capture**: Automatic screenshots on test failures with timestamps
- **Modular Fixtures**: Reusable WebDriver setup with proper cleanup
- **Performance Monitoring**: Execution time tracking and resource usage

#### **Best Practices Integration:**
- **Error Handling**: Comprehensive exception handling with detailed logging
- **Element Strategies**: Multiple locator strategies with fallback options
- **Data Management**: Realistic test data generation with validation
- **Reporting**: Detailed test reports with artifacts and metrics
- **Maintainability**: Clean, readable code with proper documentation

## 🔧 Troubleshooting

### **Common Issues & Solutions**

#### **API Key & Configuration Issues**
- **Problem**: "Invalid API key" or "Authentication failed" errors
- **Solutions**:
  - Verify your Google AI API key in `config.json`
  - Check that billing is enabled for your Google Cloud project
  - Ensure the API key has access to Gemini models
  - Try setting the API key as an environment variable: `export GOOGLE_API_KEY="your_key"`

#### **Application Startup Issues**
- **Problem**: Application fails to start or shows import errors
- **Solutions**:
  - Reinstall dependencies: `pip install -r requirements.txt --force-reinstall`
  - Check Python version: Requires Python 3.8+
  - Verify all required files are present in the directory
  - Clear Python cache: `python -m py_compile app.py`

#### **Interactive Element Selection Issues**
- **Problem**: Browser window doesn't open or elements can't be selected
- **Solutions**:
  - Update Chrome browser to the latest version
  - Install/update ChromeDriver: `pip install --upgrade selenium`
  - Check if Chrome is in PATH: `which google-chrome` (Linux/Mac)
  - Try running in non-headless mode for debugging
  - Disable browser extensions that might interfere

#### **Script Generation Issues**
- **Problem**: Generated scripts are incomplete or contain errors
- **Solutions**:
  - Verify Excel file format matches requirements
  - Ensure test steps are clear and detailed
  - Check that the website URL is accessible
  - Review AI logs in `ai_logs/` directory for detailed error information
  - Try regenerating with different prompts or test data

#### **Performance Issues**
- **Problem**: Application runs slowly or becomes unresponsive
- **Solutions**:
  - Close unnecessary browser tabs and applications
  - Clear the `ai_logs/` directory if it becomes too large
  - Restart the application to clear session state
  - Check system resources (CPU, memory usage)
  - Enable debug mode to identify bottlenecks: `export SCRIPTWEAVER_DEBUG=true`

#### **State Management Issues**
- **Problem**: Application gets stuck in a particular stage or state
- **Solutions**:
  - Use the sidebar navigation to jump to different stages
  - Clear browser cache and cookies
  - Restart the Streamlit application
  - Check the debug panel for state information
  - Reset specific state variables using the debug interface

### **Debug Mode**

Enable comprehensive debugging:
```bash
export SCRIPTWEAVER_DEBUG=true
streamlit run app.py
```

This provides:
- Detailed logging for all operations
- Stage transition monitoring
- AI request/response logging
- Performance metrics
- State change tracking

### **Log Files**

Check these locations for detailed error information:
- **AI Logs**: `ai_logs/errors/` - AI interaction errors
- **Debug Logs**: `debug_logs/` - Application debug information
- **Performance Logs**: `ai_logs/metrics/` - Performance data
- **Console Output**: Streamlit terminal output for immediate errors

### **Getting Help**

For enterprise customers with valid support agreements:
- **Primary Support**: <EMAIL>
- **Include**: Error logs, configuration details, and steps to reproduce
- **Response Time**: Based on your support agreement level

## 🔗 Integration with GRETAH Ecosystem

GretahAI ScriptWeaver seamlessly integrates with the complete GRETAH test automation ecosystem:

### **GretahAI CaseForge Integration**
- **Test Case Import**: Direct import of test cases from CaseForge with metadata preservation
- **Format Compatibility**: Automatic conversion of CaseForge test case formats
- **Workflow Continuity**: Seamless transition from test case design to automation

### **GretahAI TestInsight Integration**
- **Script Export**: Export generated test scripts for execution and analysis
- **Results Integration**: Import execution results back into TestInsight for reporting
- **Performance Analytics**: Comprehensive test execution metrics and trends

### **Enterprise Ecosystem Benefits**
- **Unified Workflow**: Complete test lifecycle management from design to execution
- **Data Consistency**: Consistent test data and metadata across all GRETAH applications
- **Centralized Reporting**: Consolidated reporting and analytics across the entire test suite
- **Team Collaboration**: Shared test assets and collaborative development workflows

## Commercial Licensing

**GretahAI ScriptWeaver is proprietary commercial software developed by Cogniron.**

- **License Type**: Commercial/Proprietary License
- **Usage Rights**: Valid commercial license required for all use
- **Distribution**: Unauthorized distribution prohibited
- **Modifications**: Source code modifications require explicit written permission
- **Enterprise Features**: Advanced features available with enterprise licensing

For licensing inquiries and terms, contact <EMAIL>

## Enterprise Development & Partnerships

**Custom Development Services Available**:
- **Custom Stage Development**: Tailored workflow stages for specific business needs
- **Enterprise Integration**: Custom integrations with existing testing infrastructure
- **Advanced AI Features**: Enhanced AI capabilities for enterprise environments
- **Professional Services**: Implementation, training, and ongoing support

**Partnership Opportunities**:
- Technology integration partnerships
- Reseller and distribution partnerships
- Enterprise deployment partnerships

Contact <EMAIL> for partnership discussions.

## Commercial Support & Contact

**Primary Support Contact**: <EMAIL>

**Commercial Services Available**:
- Enterprise licensing and deployment
- Custom feature development and integration
- Professional training and certification programs
- Dedicated technical support with SLA guarantees
- Architecture consulting and best practices guidance

**Website**: https://cogniron.com

**Note**: This is commercial software intended for professional use. Enterprise evaluation licenses are available upon request. All development and customization requires a valid commercial agreement.
